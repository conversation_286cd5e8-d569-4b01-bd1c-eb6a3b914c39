package com.wosai.upay.job.refactor.task.license.micro;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.biz.IndustryMappingCommonBiz;
import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.providers.HaikeProvider;
import com.wosai.upay.job.refactor.biz.acquirer.haike.HaikeAcquirerFacade;
import com.wosai.upay.job.refactor.event.HaikeUnionPayContractSuccessEvent;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.service.factory.McJobThreadPoolFactory;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationTask;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationV3Task;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseCertificationV3MainTaskContext;
import com.wosai.upay.job.refactor.utils.BeanCopyUtils;
import com.wosai.upay.job.refactor.utils.ThreadPoolWorker;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.HaikeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 海科营业执照认证重新入网成功后参数处理
 *
 * <AUTHOR>
 * @date 2024/9/13 16:24
 */
@Component
@Slf4j
public class HaikeUpdateTradeParams extends AbstractUpdateTradeParamsTemplate {


    @Resource
    private ProviderTerminalBiz providerTerminalBiz;

    @Resource
    private HaikeService haikeService;

    @Resource
    private HaikeProvider haikeProvider;


    @Override
    public String getAcquirer() {
        return AcquirerTypeEnum.HAI_KE.getValue();
    }

    @Override
    Integer getProvider() {
        return ProviderEnum.PROVIDER_HAIKE.getValue();
    }

    private final BlockingQueue<MerchantProviderParamsDO> retrySyncToHaikePayMerchantIds = new LinkedBlockingQueue<>(50000);

    @Override
    void rollbackTradeParamsForPayIsNull(MerchantInfo merchantInfo, InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenceCertificationTask.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationTask.SubTaskContextBOInner.class);
        if (Objects.isNull(subTaskContextBOInner)) {
            return;
        }
        tradeConfigService.updateHKTradeParams(merchantInfo.getId(), CollectionUtil.hashMap("hk_mch_id", subTaskContextBOInner.getOldAcquirerMerchantId()));
    }

    @Override
    public String getContractTermNo(String merchantSn) {
        // 暂时用不到
        return "";
    }

    @Override
    public String getMerchantConfigProviderMerchantIdKey() {
        return "provider_mch_id";
    }

    @Override
    public void updateClearanceProviderWhenSameAcquirer(String merchantId) {
        coreBTradeConfigService.updateMerchantSwitchMchTime(merchantId, TransactionParam.CLEARANCE_PROVIDER_HAIKE, System.currentTimeMillis());
        tradeConfigService.updateClearanceProvider(merchantId, TransactionParam.CLEARANCE_PROVIDER_HAIKE);
    }

    @Override
    void asyncExistedStoreAndTerminalBind(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        ThreadPoolWorker.of(McJobThreadPoolFactory.getInstance()).submit(() -> {
            haikeProvider.doCreateProviderTerminal(subTaskDO.getMerchantSn(), ProviderEnum.PROVIDER_HAIKE.getValue());
            // contractExistedTerminalsAndBindToPayWayForHaike(subTaskDO);
        });
    }

    @Override
    void asyncExistedStoreAndTerminalBindV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        asyncExistedStoreAndTerminalBind(mainTaskDO,subTaskDO);
    }

    private void contractExistedTerminalsAndBindToPayWayForHaike(InternalScheduleSubTaskDO subTaskDO) {
        // 获取所有的收钱吧终端号
        List<Map> terminals = merchantBasicInfoBiz.listAllSqbTerminals(subTaskDO.getMerchantSn());
        if (CollectionUtils.isEmpty(terminals)) {
            log.warn("商户{}没有终端", subTaskDO.getMerchantSn());
            return;
        }
        MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoBySn(subTaskDO.getMerchantSn());
        for (Map terminal : terminals) {
            try {
                haikeProvider.handleSqbTerminalBind(merchantInfo, terminal, subTaskDO.getProvider());
            } catch (Exception e) {
                log.error("商户{}终端{}绑定失败", subTaskDO.getMerchantSn(), terminal.get("terminal_sn"), e);
            }
        }
    }

    @Override
    void acquirerSpecialProcess(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        // nothing todo yet
    }

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Override
    void acquirerSpecialProcessV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        applicationEventPublisher.publishEvent(new HaikeUnionPayContractSuccessEvent(this, mainTaskDO.getMerchantSn()));
    }

    @Resource
    private ChatBotUtil chatBotUtil;

    @Override
    void addMerchantLevelStoreAndTerminalBind(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO,
                                              MerchantInfo merchantInfo, List<MerchantProviderParamsDO> newParams) {
        BusinessLicenceCertificationTask.SubTaskResponseDTOInner subTaskResponseDTOInner = JSON.parseObject(subTaskDO.getResponseMessage(), BusinessLicenceCertificationTask.SubTaskResponseDTOInner.class);
        providerTerminalBiz.merchantConnectionProviderTerminal(subTaskDO.getMerchantSn(), subTaskResponseDTOInner.getNewTermId(), subTaskResponseDTOInner.getNewAcquirerMerchantId(), subTaskDO.getProvider());
        Map<Integer, List<MerchantProviderParamsDO>> payPramsMap = newParams.stream().collect(Collectors.groupingBy(MerchantProviderParamsDO::getPayway));
        if (payPramsMap.containsKey(PaywayEnum.WEIXIN.getValue())) {
            for (MerchantProviderParamsDO merchantProviderParamsDO : payPramsMap.get(PaywayEnum.WEIXIN.getValue())) {
                syncATToHaike( merchantProviderParamsDO);
                log.info("小微升级海科商户微信子商户号同步成功，merchantSn:{}, 任务id:{}", subTaskDO.getMerchantSn(), mainTaskDO.getId());
            }
        }
        if (payPramsMap.containsKey(PaywayEnum.ALIPAY.getValue())) {
            for (MerchantProviderParamsDO merchantProviderParamsDO : payPramsMap.get(PaywayEnum.ALIPAY.getValue())) {
                syncATToHaike(merchantProviderParamsDO);
                log.info("小微升级海科商户支付宝子商户号同步成功，merchantSn:{}, 任务id:{}", subTaskDO.getMerchantSn(), mainTaskDO.getId());
            }

        }
        addPayWayParamsTerminalBindTask(subTaskDO, newParams);
    }

    @Override
    void addMerchantLevelStoreAndTerminalBindV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO, MerchantInfo merchantInfo, List<MerchantProviderParamsDO> newParams) {

        BusinessLicenseCertificationV3MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(),
                BusinessLicenseCertificationV3MainTaskContext.class);
        final Long mainContractTaskId = mainTaskContextBOInner.getMainContractTaskId();
        final MerchantAcquireInfoBO merchantAcquireInfoBO = haikeAcquirerFacade.getAcquireInfoFromContractSubTask(mainContractTaskId);

        providerTerminalBiz.merchantConnectionProviderTerminal(subTaskDO.getMerchantSn(), merchantAcquireInfoBO.getHaikeTermId(), merchantAcquireInfoBO.getAcquireMerchantId(), subTaskDO.getProvider());
        Map<Integer, List<MerchantProviderParamsDO>> payPramsMap = newParams.stream().collect(Collectors.groupingBy(MerchantProviderParamsDO::getPayway));
        if (payPramsMap.containsKey(PaywayEnum.WEIXIN.getValue())) {
            for (MerchantProviderParamsDO merchantProviderParamsDO : payPramsMap.get(PaywayEnum.WEIXIN.getValue())) {
                syncATToHaike( merchantProviderParamsDO);
                log.info("小微升级海科商户微信子商户号同步成功，merchantSn:{}, 任务id:{}", subTaskDO.getMerchantSn(), mainTaskDO.getId());
            }
        }
        if (payPramsMap.containsKey(PaywayEnum.ALIPAY.getValue())) {
            for (MerchantProviderParamsDO merchantProviderParamsDO : payPramsMap.get(PaywayEnum.ALIPAY.getValue())) {
                syncATToHaike(merchantProviderParamsDO);
                log.info("小微升级海科商户支付宝子商户号同步成功，merchantSn:{}, 任务id:{}", subTaskDO.getMerchantSn(), mainTaskDO.getId());
            }

        }
        addPayWayParamsTerminalBindTask(subTaskDO, newParams);
    }

    @Scheduled(fixedRate = 5000)
    public void syncFailedPayMerchantIdToHaikeScheduleJob() {
        MerchantProviderParamsDO paramsDO = retrySyncToHaikePayMerchantIds.poll();
        if (Objects.isNull(paramsDO)) {
            return;
        }
        try {
            ContractResponse contractResponse = haikeService.syncTerminalAndMerchant(paramsDO.getPayMerchantId());
            if (!contractResponse.isSuccess()) {
                log.error("小微升级海科商户AT子商户号同步失败，merchantSn:{}, payWay:{}, 返回报文:{}", paramsDO.getMerchantSn(), paramsDO.getPayway(), contractResponse);
                chatBotUtil.sendMessageToMicroUpgradeChatBot("小微升级AT子商户号同步失败，merchantSn:" + paramsDO.getMerchantSn() + ", payWay:" + paramsDO.getPayway() + ", payMerchantId:" + paramsDO.getPayMerchantId());
            }
        } catch (Exception e) {
            log.error("小微升级海科商户AT子商户号同步失败，merchantSn:{}, payWay:{}", paramsDO.getMerchantSn(), paramsDO.getPayway(), e);
            chatBotUtil.sendMessageToMicroUpgradeChatBot("小微升级AT子商户号同步失败，merchantSn:" + paramsDO.getMerchantSn() + ", payWay:" + paramsDO.getPayway() + ", payMerchantId:" + paramsDO.getPayMerchantId());
        }
    }

    private void syncATToHaike(MerchantProviderParamsDO payPrams) {
        try {
            ContractResponse contractResponse = haikeService.syncTerminalAndMerchant(payPrams.getPayMerchantId());
            if (!contractResponse.isSuccess()) {
                retrySyncToHaikePayMerchantIds.put(payPrams);
            }
        } catch (Exception e) {
            retrySyncToHaikePayMerchantIds.add(payPrams);
        }
    }

    @Override
    void updateTradeParamsForPayIsNull(MerchantInfo merchantInfo, InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenceCertificationTask.SubTaskResponseDTOInner subTaskResponseDTOInner = JSON.parseObject(subTaskDO.getResponseMessage(), BusinessLicenceCertificationTask.SubTaskResponseDTOInner.class);
        String merchantId = merchantBasicInfoBiz.getMerchantIdByMerchantSn(subTaskDO.getMerchantSn()).orElseThrow(() -> new ContractBizException("商户不存在"));
        tradeConfigService.updateHKTradeParams(merchantId, CollectionUtil.hashMap("hk_mch_id", subTaskResponseDTOInner.getNewAcquirerMerchantId()));
    }

    @Override
    Map<String, MerchantProviderParamsDO> buildNewNeedInsertParamsAndReturnOldNewMap(InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenceCertificationTask.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationTask.SubTaskContextBOInner.class);
        BusinessLicenceCertificationTask.SubTaskResponseDTOInner subTaskResponseDTOInner = JSON.parseObject(subTaskDO.getResponseMessage(), BusinessLicenceCertificationTask.SubTaskResponseDTOInner.class);
        Map<String, MerchantProviderParamsDO> oldNewParamsMap = Maps.newHashMap();
        for (MerchantProviderParamsDO params : subTaskContextBOInner.getOldPayWayParamsMap().values().stream().flatMap(Collection::stream).collect(Collectors.toList())) {
            MerchantProviderParamsDO newAcquirerParams = BeanCopyUtils.copyProperties(params, MerchantProviderParamsDO.class);
            newAcquirerParams.setId(UUID.randomUUID().toString());
            newAcquirerParams.setCtime(System.currentTimeMillis());
            newAcquirerParams.setMtime(System.currentTimeMillis());
            newAcquirerParams.setProviderMerchantId(subTaskResponseDTOInner.getNewAcquirerMerchantId());
            if (Objects.equals(params.getPayway(), PaywayEnum.ACQUIRER.getValue())) {
                newAcquirerParams.setParentMerchantId(subTaskResponseDTOInner.getNewAcquirerMerchantId());
                newAcquirerParams.setPayMerchantId(subTaskResponseDTOInner.getNewAcquirerMerchantId());
                HashMap<String, String> extra = new HashMap<>();
                extra.put("bankMerchNo", subTaskResponseDTOInner.getNewUnionMerchantId());
                newAcquirerParams.setExtra(JSON.toJSONString(extra));
            } else if (Objects.equals(params.getPayway(), PaywayEnum.UNIONPAY.getValue())) {
                newAcquirerParams.setPayMerchantId(subTaskResponseDTOInner.getNewUnionMerchantId());
                HashMap<String, String> extra = new HashMap<>();
                extra.put("bankMerchNo", subTaskResponseDTOInner.getNewUnionMerchantId());
                newAcquirerParams.setExtra(JSON.toJSONString(extra));
            }
            oldNewParamsMap.put(params.getId(), newAcquirerParams);
        }
        return oldNewParamsMap;
    }


    @Resource
    private HaikeAcquirerFacade haikeAcquirerFacade;

    @Resource
    private IndustryMappingCommonBiz industryMappingCommonBiz;
    @Resource
    private WechatAuthBiz wechatAuthBiz;
    @Resource
    @Lazy
    private BusinessLicenceCertificationV3Task businessLicenceCertificationV3Task;


    @Override
    Map<String, MerchantProviderParamsDO> buildNewNeedInsertParamsAndReturnOldNewMapV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenseCertificationV3MainTaskContext mainCtx = JSON.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV3MainTaskContext.class);
        final Map<String, Object> merchant = mainCtx.getMerchant();
        final String wxAuthTime = mainCtx.getWxAuthTime();
        final Long aliAuthTime = mainCtx.getAliAuthTime();
        BusinessLicenceCertificationV3Task.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationV3Task.SubTaskContextBOInner.class);

        Map<String, MerchantProviderParamsDO> oldNewParamsMap = Maps.newHashMap();
        final MerchantAcquireInfoBO merchantAcquireInfoBO = haikeAcquirerFacade.getAcquireInfoFromContractSubTask(subTaskContextBOInner.getContractTaskId());

        for (MerchantProviderParamsDO params : subTaskContextBOInner.getOldPayWayParamsMap().values().stream().flatMap(Collection::stream).collect(Collectors.toList())) {
            MerchantProviderParamsDO newParam = BeanCopyUtils.copyProperties(params, MerchantProviderParamsDO.class);
            newParam.setId(UUID.randomUUID().toString());
            newParam.setCtime(System.currentTimeMillis());
            newParam.setMtime(System.currentTimeMillis());
            newParam.setProviderMerchantId(merchantAcquireInfoBO.getAcquireMerchantId());
            //payway=0的参数
            if(Objects.equals(params.getPayway(), PaywayEnum.ACQUIRER.getValue())) {
                newParam.setPayMerchantId(merchantAcquireInfoBO.getAcquireMerchantId());
            }else if (Objects.equals(params.getPayway(), PaywayEnum.ALIPAY.getValue())) {
                //同一个子商户号只能出现一次,对于在收单机构下有多个支付宝参数的情况下,小微升级以后只允许出现一个
                final boolean present = oldNewParamsMap.values().stream().filter(param -> Objects.equals(param.getPayway(), PaywayEnum.ALIPAY.getValue())
                                && Objects.equals(param.getPayMerchantId(), merchantAcquireInfoBO.getAliNo()))
                        .findFirst()
                        .isPresent();
                if(present) {
                    continue;
                }
                final Map<String, Object> contractParamContext = businessLicenceCertificationV3Task.buildContractContext(mainTaskDO);
                newParam.setPayMerchantId(merchantAcquireInfoBO.getAliNo());
                String industryId = BeanUtil.getPropString(merchant, Merchant.INDUSTRY);
                String aliMcc = industryMappingCommonBiz.getAliIndirectMcc(industryId);
                newParam.setAliMcc(aliMcc);
                newParam.setMerchantName(wechatAuthBiz.getWechatAuthMerchantName(contractParamContext));
                newParam.setExtra(JSONObject.toJSONString(CollectionUtil.hashMap(AUTH_TIME,aliAuthTime)));
            }else if (Objects.equals(params.getPayway(), PaywayEnum.WEIXIN.getValue())) {
                //同一个子商户号只能出现一次,对于在收单机构下有多个微信参数的情况下,小微升级以后只允许出现一个
                final boolean present = oldNewParamsMap.values().stream().filter(param -> Objects.equals(param.getPayway(), PaywayEnum.WEIXIN.getValue())
                                && Objects.equals(param.getPayMerchantId(), merchantAcquireInfoBO.getWxNo()))
                        .findFirst()
                        .isPresent();
                if(present) {
                    continue;
                }
                newParam.setPayMerchantId(merchantAcquireInfoBO.getWxNo());

                final Map<String, Object> contractParamContext = businessLicenceCertificationV3Task.buildContractContext(mainTaskDO);
                final WechatAuthBiz.WechatAuthNameAndSettId merchantNameAndSettlementId = wechatAuthBiz.getMerchantNameAndSettlementId(contractParamContext);
                newParam.setWeixinSubAppid(null);
                newParam.setWeixinSubMiniAppid(null);
                newParam.setWxSettlementId(merchantNameAndSettlementId.getSettlementId());
                newParam.setMerchantName(merchantNameAndSettlementId.getMerchantName());
                newParam.setParamsConfigStatus(MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE);
                newParam.setExtra(JSONObject.toJSONString(CollectionUtil.hashMap(AUTH_TIME,wxAuthTime)));
            }else if (Objects.equals(params.getPayway(), PaywayEnum.UNIONPAY.getValue())) {
                //同一个子商户号只能出现一次,对于在收单机构下有多个云闪付参数的情况下,小微升级以后只允许出现一个
                final boolean present = oldNewParamsMap.values().stream().filter(param -> Objects.equals(param.getPayway(), PaywayEnum.UNIONPAY.getValue())
                                && Objects.equals(param.getPayMerchantId(), merchantAcquireInfoBO.getUnionNo()))
                        .findFirst()
                        .isPresent();
                if(present) {
                    continue;
                }
                newParam.setMerchantName(merchantAcquireInfoBO.getHaikeUnionName());
                newParam.setPayMerchantId(merchantAcquireInfoBO.getUnionNo());
                HashMap<String, Object> extra = new HashMap<>();
                extra.put("tradeParams",CollectionUtil.hashMap("bankMerchNo", merchantAcquireInfoBO.getUnionNo()));
                newParam.setExtra(JSON.toJSONString(extra));
            }
            oldNewParamsMap.put(params.getId(), newParam);
        }
        return oldNewParamsMap;
    }


}

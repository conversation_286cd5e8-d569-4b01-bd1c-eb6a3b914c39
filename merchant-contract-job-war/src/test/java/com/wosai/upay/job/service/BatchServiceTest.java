package com.wosai.upay.job.service;


import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.BatchResq;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


public class BatchServiceTest extends BaseTest {


    @Autowired
    BatchService batchService;

    @Test
    public void batchContract() {
        batchService.batchContract(getRequest());
    }

    @Test
    public void batchChangeByFile() {
        batchService.batchChangeByFile(getRequest());
    }

    @Test
    public void batchChangeByRule() {
        batchService.batchChangeByRule(getRequest());
    }

    private BatchResq getRequest() {
        BatchResq batchResq = new BatchResq();
        batchResq.setRule("lkl-1016-3-32631798");
        batchResq.setEffectTime(System.currentTimeMillis());
        batchResq.setOldRule("lkl-1016-3-32631798");
        batchResq.setUrl("sasasa");
        batchResq.setRemark("备注");
        batchResq.setFeeRate("0.38");
        return batchResq;
    }

    @Test
    public void commonImportTest() {
        List<String> row = Lists.newArrayList(
                "21690003809348",
                "1044",
                "4175912AAAAAAAA",
                "2088740064532490",
                "652540988",
                "4175912AAAAAAAA",
                "0.25",
                "15455",
                "入网自动化小微",
                "4127",
                "MIIClwIBATBHBgoqgRzPVQYBBAIBBgcqgRzPVQFoBDCrgg5ftvRQCFvpdnSjj3XRDTh3w1n4LSVgkaa2AEaNg620vKVt01P2WgrAr/+OCEswggJHBgoqgRzPVQYBBAIBBIICNzCCAjMwggHaoAMCAQICBgGO3yTycTAKBggqgRzPVQGDdTB6MRgwFgYDVQQDEw80MTczOTExNTMxMTBaMzMxIjAgBgNVBAsTGUZVSklBTiBSVVJBTCBDUkVESVQgVU5JT04xCzAJBgNVBAoTAklUMQ8wDQYDVQQHEwZGVVpIT1UxDzANBgNVBAgTBkZVSklBTjELMAkGA1UEBhMCQ04wHhcNMjQwNDE1MDAyNTI0WhcNMzQwNDE1MDAyNTI0WjB6MRgwFgYDVQQDEw80MTczOTExNTMxMTBaMzMxIjAgBgNVBAsTGUZVSklBTiBSVVJBTCBDUkVESVQgVU5JT04xCzAJBgNVBAoTAklUMQ8wDQYDVQQHEwZGVVpIT1UxDzANBgNVBAgTBkZVSklBTjELMAkGA1UEBhMCQ04wWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAAQoIZzPPWFWg00ORr6fhU2nzrs32V4GsCaLXqkCXKOERtQzepYzFHhalipxAniIyGoIWavw8tXG5tUavp+sgtio0wwSjALBgNVHQ8EBAMCBsAwCQYDVR0TBAIwADARBglghkgBhvhCAQEEBAMCB4AwHQYDVR0lBBYwFAYIKwYBBQUHAwIGCCsGAQUFBwMEMAoGCCqBHM9VAYN1A0cAMEQCIB1eoJP1pUVv0aLkSulJgzymFrtyruwAamDWgS7TRv5AiBQo+BADuwnzdvjUdjtagtkPt4w/keIMC80HJ8d/1u4+Q==",
                "1234",
                "上海收钱吧互联网科技股份有限公司",
                "test1",
                "dddd"
        );
        String operator = "test11";
        batchService.commonImport(row, operator);

    }
}
